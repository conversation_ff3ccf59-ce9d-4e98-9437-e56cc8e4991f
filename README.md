# 移动端电商应用

这是一个基于 Next.js 开发的移动端电商应用，支持静态导出。

## 功能特性

- 📱 移动端优化设计
- 🛒 完整的购物流程
- 📦 订单管理系统
- 👤 用户中心
- 🏷️ 商品分类浏览
- 💳 多种支付方式
- 🚚 配送方式选择

## 静态导出

本应用支持静态导出，可以部署到任何静态网站托管服务。

### 构建命令

\`\`\`bash
npm run build
\`\`\`

构建完成后，静态文件将生成在 `out` 目录中。

### 部署

可以将 `out` 目录中的文件部署到：

- GitHub Pages
- Vercel
- Netlify
- 任何静态网站托管服务

### 本地预览

\`\`\`bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建静态版本
npm run build

# 预览构建结果
npx serve out
\`\`\`

## 技术栈

- Next.js 14 (App Router)
- React 18
- TypeScript
- Tailwind CSS
- shadcn/ui
- Lucide React Icons

## 项目结构

\`\`\`
├── app/                 # 应用页面
│   ├── page.tsx        # 首页
│   ├── cart/           # 购物车
│   ├── orders/         # 订单管理
│   ├── product/        # 商品详情
│   ├── profile/        # 用户中心
│   └── category/       # 商品分类
├── components/         # 组件
├── public/            # 静态资源
└── lib/               # 工具函数
\`\`\`

## 注意事项

由于是静态导出版本，以下功能需要在实际部署时集成：

- 用户认证系统
- 支付接口
- 数据库连接
- 服务端API
- 实时数据更新

## 自定义配置

可以通过修改以下文件来自定义应用：

- `app/static-data.ts` - 静态数据配置
- `tailwind.config.ts` - 样式配置
- `next.config.mjs` - Next.js 配置
