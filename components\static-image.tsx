"use client"

import { useState } from "react"

interface StaticImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  fill?: boolean
}

export function StaticImage({ src, alt, width, height, className, fill }: StaticImageProps) {
  const [error, setError] = useState(false)

  if (fill) {
    return (
      <img
        src={error ? "/placeholder.svg" : src}
        alt={alt}
        className={`absolute inset-0 w-full h-full object-cover ${className || ""}`}
        onError={() => setError(true)}
      />
    )
  }

  return (
    <img
      src={error ? "/placeholder.svg" : src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onError={() => setError(true)}
    />
  )
}
