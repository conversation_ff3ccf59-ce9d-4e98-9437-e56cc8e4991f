"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Package, Truck, CheckCircle, Clock, MessageCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { StaticImage } from "@/components/static-image"

export default function OrdersPage() {
  const [activeTab, setActiveTab] = useState("all")

  const orders = [
    {
      id: "202401150001",
      status: "delivered",
      statusText: "已完成",
      date: "2024-01-15 14:30",
      total: 85.6,
      items: [
        {
          id: 1,
          name: "新鲜有机苹果",
          image: "/placeholder.svg?height=60&width=60",
          spec: "500g",
          quantity: 2,
          price: 28.8,
        },
        {
          id: 2,
          name: "优质大米",
          image: "/placeholder.svg?height=60&width=60",
          spec: "5kg",
          quantity: 1,
          price: 28.0,
        },
      ],
    },
    {
      id: "202401140002",
      status: "shipping",
      statusText: "配送中",
      date: "2024-01-14 10:20",
      total: 45.8,
      items: [
        {
          id: 3,
          name: "新鲜蔬菜包",
          image: "/placeholder.svg?height=60&width=60",
          spec: "混合装",
          quantity: 1,
          price: 25.8,
        },
      ],
    },
    {
      id: "202401130003",
      status: "pending",
      statusText: "待支付",
      date: "2024-01-13 16:45",
      total: 68.8,
      items: [
        {
          id: 4,
          name: "精选牛肉",
          image: "/placeholder.svg?height=60&width=60",
          spec: "500g",
          quantity: 1,
          price: 68.8,
        },
      ],
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4 text-orange-500" />
      case "shipping":
        return <Truck className="w-4 h-4 text-blue-500" />
      case "delivered":
        return <CheckCircle className="w-4 h-4 text-green-500" />
      default:
        return <Package className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-orange-500"
      case "shipping":
        return "text-blue-500"
      case "delivered":
        return "text-green-500"
      default:
        return "text-gray-500"
    }
  }

  const filteredOrders = activeTab === "all" ? orders : orders.filter((order) => order.status === activeTab)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white sticky top-0 z-50 border-b">
        <div className="flex items-center justify-between p-4">
          <Link href="/">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </Link>
          <span className="font-medium">我的订单</span>
          <div className="w-10"></div>
        </div>
      </header>

      {/* Order Tabs */}
      <div className="bg-white">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 rounded-none h-12">
            <TabsTrigger value="all" className="text-sm">
              全部
            </TabsTrigger>
            <TabsTrigger value="pending" className="text-sm">
              待支付
            </TabsTrigger>
            <TabsTrigger value="shipping" className="text-sm">
              配送中
            </TabsTrigger>
            <TabsTrigger value="delivered" className="text-sm">
              已完成
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Orders List */}
      <div className="p-4 space-y-3 pb-20">
        {filteredOrders.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-96">
            <div className="text-6xl mb-4">📋</div>
            <p className="text-gray-500 mb-4">暂无订单</p>
            <Link href="/">
              <Button>去购物</Button>
            </Link>
          </div>
        ) : (
          filteredOrders.map((order) => (
            <Card key={order.id}>
              <CardContent className="p-4">
                {/* Order Header */}
                <div className="flex items-center justify-between mb-3 pb-3 border-b">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(order.status)}
                    <span className={`font-medium ${getStatusColor(order.status)}`}>{order.statusText}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">订单号: {order.id}</div>
                    <div className="text-xs text-gray-400">{order.date}</div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="space-y-3 mb-3">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex items-center gap-3">
                      <StaticImage
                        src={item.image || "/placeholder.svg"}
                        alt={item.name}
                        width={60}
                        height={60}
                        className="w-15 h-15 object-cover rounded"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-sm line-clamp-1">{item.name}</h4>
                        <p className="text-xs text-gray-500">{item.spec}</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-sm">¥{item.price}</span>
                          <span className="text-xs text-gray-500">x{item.quantity}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Order Footer */}
                <div className="pt-3 border-t space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">共{order.items.length}件商品</span>
                      <span className="text-sm">合计: </span>
                      <span className="font-bold text-red-500">¥{order.total}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 justify-end">
                    {order.status === "pending" && (
                      <Button size="sm" className="bg-red-500 hover:bg-red-600">
                        立即支付
                      </Button>
                    )}
                    {order.status === "shipping" && (
                      <Button size="sm" variant="outline">
                        查看物流
                      </Button>
                    )}
                    {order.status === "delivered" && (
                      <>
                        <Button size="sm" variant="outline">
                          申请售后
                        </Button>
                        <Button size="sm" variant="outline">
                          再次购买
                        </Button>
                      </>
                    )}
                    <Button size="sm" variant="outline">
                      <MessageCircle className="w-3 h-3 mr-1" />
                      客服
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="flex">
          <Link href="/" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/category" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">📂</div>
            <span className="text-xs">分类</span>
          </Link>
          <Link href="/cart" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">🛒</div>
            <span className="text-xs">购物车</span>
          </Link>
          <Link href="/orders" className="flex-1 flex flex-col items-center py-2 text-blue-500">
            <div className="w-6 h-6 mb-1">📋</div>
            <span className="text-xs">订单</span>
          </Link>
          <Link href="/profile" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">👤</div>
            <span className="text-xs">我的</span>
          </Link>
        </div>
      </nav>
    </div>
  )
}
