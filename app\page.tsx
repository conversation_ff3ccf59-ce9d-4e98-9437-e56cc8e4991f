"use client"

import { useState } from "react"
import { StaticImage } from "@/components/static-image"
import Link from "next/link"
import { Search, ShoppingCart, User, Clock, FlameIcon as Fire, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function HomePage() {
  const [currentBanner, setCurrentBanner] = useState(0)

  const banners = [
    { id: 1, image: "/placeholder.svg?height=200&width=400", title: "新年大促销", subtitle: "全场8折起" },
    { id: 2, image: "/placeholder.svg?height=200&width=400", title: "生鲜特价", subtitle: "新鲜直达" },
    { id: 3, image: "/placeholder.svg?height=200&width=400", title: "爆款推荐", subtitle: "限时抢购" },
  ]

  const categories = [
    { id: 1, name: "生鲜", icon: "🥬", color: "bg-green-100" },
    { id: 2, name: "干货", icon: "🌰", color: "bg-yellow-100" },
    { id: 3, name: "调料", icon: "🧂", color: "bg-red-100" },
    { id: 4, name: "零食", icon: "🍪", color: "bg-purple-100" },
    { id: 5, name: "饮品", icon: "🥤", color: "bg-blue-100" },
    { id: 6, name: "肉类", icon: "🥩", color: "bg-pink-100" },
    { id: 7, name: "水果", icon: "🍎", color: "bg-orange-100" },
    { id: 8, name: "更多", icon: "➕", color: "bg-gray-100" },
  ]

  const flashSaleProducts = [
    {
      id: 1,
      name: "新鲜苹果",
      price: 12.8,
      originalPrice: 18.8,
      image: "/placeholder.svg?height=100&width=100",
      timeLeft: "02:30:45",
    },
    {
      id: 2,
      name: "优质大米",
      price: 28.8,
      originalPrice: 35.8,
      image: "/placeholder.svg?height=100&width=100",
      timeLeft: "02:30:45",
    },
    {
      id: 3,
      name: "有机蔬菜",
      price: 15.8,
      originalPrice: 22.8,
      image: "/placeholder.svg?height=100&width=100",
      timeLeft: "02:30:45",
    },
  ]

  const recommendProducts = [
    { id: 1, name: "精选牛肉", price: 68.8, image: "/placeholder.svg?height=150&width=150", rating: 4.8, sales: 1200 },
    { id: 2, name: "新鲜蔬菜包", price: 25.8, image: "/placeholder.svg?height=150&width=150", rating: 4.9, sales: 800 },
    { id: 3, name: "优质海鲜", price: 88.8, image: "/placeholder.svg?height=150&width=150", rating: 4.7, sales: 600 },
    { id: 4, name: "有机水果", price: 35.8, image: "/placeholder.svg?height=150&width=150", rating: 4.8, sales: 900 },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white sticky top-0 z-50 border-b">
        <div className="flex items-center gap-3 p-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="搜索商品" className="pl-10 rounded-full" />
          </div>
          <Link href="/cart">
            <Button variant="ghost" size="icon" className="relative">
              <ShoppingCart className="w-5 h-5" />
              <Badge className="absolute -top-1 -right-1 w-5 h-5 rounded-full p-0 flex items-center justify-center text-xs">
                3
              </Badge>
            </Button>
          </Link>
          <Link href="/profile">
            <Button variant="ghost" size="icon">
              <User className="w-5 h-5" />
            </Button>
          </Link>
        </div>
      </header>

      {/* Banner Carousel */}
      <div className="relative overflow-hidden">
        <div
          className="flex transition-transform duration-300"
          style={{ transform: `translateX(-${currentBanner * 100}%)` }}
        >
          {banners.map((banner) => (
            <div key={banner.id} className="w-full flex-shrink-0">
              <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
                <StaticImage
                  src={banner.image || "/placeholder.svg"}
                  alt={banner.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                  <div className="text-center text-white">
                    <h2 className="text-2xl font-bold mb-2">{banner.title}</h2>
                    <p className="text-lg">{banner.subtitle}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
          {banners.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentBanner(index)}
              className={`w-2 h-2 rounded-full ${currentBanner === index ? "bg-white" : "bg-white/50"}`}
            />
          ))}
        </div>
      </div>

      {/* Categories */}
      <div className="bg-white p-4 mb-2">
        <div className="grid grid-cols-4 gap-4">
          {categories.map((category) => (
            <Link key={category.id} href={`/category/${category.id}`}>
              <div className="text-center">
                <div
                  className={`w-12 h-12 rounded-full ${category.color} flex items-center justify-center mx-auto mb-2`}
                >
                  <span className="text-xl">{category.icon}</span>
                </div>
                <span className="text-sm text-gray-600">{category.name}</span>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Flash Sale */}
      <div className="bg-white p-4 mb-2">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Fire className="w-5 h-5 text-red-500" />
            <h3 className="font-bold text-lg">限时秒杀</h3>
          </div>
          <div className="flex items-center gap-1 text-red-500">
            <Clock className="w-4 h-4" />
            <span className="text-sm font-mono">02:30:45</span>
          </div>
        </div>
        <div className="flex gap-3 overflow-x-auto pb-2">
          {flashSaleProducts.map((product) => (
            <Link key={product.id} href={`/product/${product.id}`}>
              <Card className="w-32 flex-shrink-0">
                <CardContent className="p-3">
                  <StaticImage
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    width={100}
                    height={100}
                    className="w-full h-20 object-cover rounded mb-2"
                  />
                  <h4 className="text-sm font-medium mb-1 line-clamp-1">{product.name}</h4>
                  <div className="flex items-center gap-1">
                    <span className="text-red-500 font-bold text-sm">¥{product.price}</span>
                    <span className="text-gray-400 line-through text-xs">¥{product.originalPrice}</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>

      {/* Recommended Products */}
      <div className="bg-white p-4">
        <h3 className="font-bold text-lg mb-4">为你推荐</h3>
        <div className="grid grid-cols-2 gap-3">
          {recommendProducts.map((product) => (
            <Link key={product.id} href={`/product/${product.id}`}>
              <Card className="overflow-hidden">
                <CardContent className="p-0">
                  <StaticImage
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    width={150}
                    height={150}
                    className="w-full h-32 object-cover"
                  />
                  <div className="p-3">
                    <h4 className="font-medium mb-2 line-clamp-2">{product.name}</h4>
                    <div className="flex items-center gap-1 mb-2">
                      <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-gray-600">{product.rating}</span>
                      <span className="text-xs text-gray-400">已售{product.sales}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-red-500 font-bold">¥{product.price}</span>
                      <Button size="sm" variant="outline" className="h-7 px-2 text-xs bg-transparent">
                        <ShoppingCart className="w-3 h-3 mr-1" />
                        加购
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="flex">
          <Link href="/" className="flex-1 flex flex-col items-center py-2 text-blue-500">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/category" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">📂</div>
            <span className="text-xs">分类</span>
          </Link>
          <Link href="/cart" className="flex-1 flex flex-col items-center py-2 text-gray-600 relative">
            <div className="w-6 h-6 mb-1">🛒</div>
            <span className="text-xs">购物车</span>
            <Badge className="absolute top-0 right-6 w-4 h-4 rounded-full p-0 flex items-center justify-center text-xs">
              3
            </Badge>
          </Link>
          <Link href="/orders" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">📋</div>
            <span className="text-xs">订单</span>
          </Link>
          <Link href="/profile" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">👤</div>
            <span className="text-xs">我的</span>
          </Link>
        </div>
      </nav>

      <div className="h-16"></div>
    </div>
  )
}
