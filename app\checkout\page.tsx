"use client"

import { useState } from "react"
import { StaticImage } from "@/components/static-image"
import Link from "next/link"
import { ArrowLeft, MapPin, Plus, Truck, CreditCard, Gift } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"

export default function CheckoutPage() {
  const [selectedAddress, setSelectedAddress] = useState(1)
  const [deliveryMethod, setDeliveryMethod] = useState("express")
  const [paymentMethod, setPaymentMethod] = useState("wechat")
  const [selectedCoupon, setSelectedCoupon] = useState("")
  const [remark, setRemark] = useState("")

  const addresses = [
    {
      id: 1,
      name: "张三",
      phone: "138****8888",
      address: "北京市朝阳区三里屯街道工体北路8号院1号楼101室",
      isDefault: true,
    },
    {
      id: 2,
      name: "李四",
      phone: "139****9999",
      address: "上海市浦东新区陆家嘴金融贸易区世纪大道100号",
      isDefault: false,
    },
  ]

  const cartItems = [
    {
      id: 1,
      name: "新鲜有机苹果",
      price: 28.8,
      image: "/placeholder.svg?height=60&width=60",
      spec: "500g",
      quantity: 2,
    },
    {
      id: 2,
      name: "优质大米",
      price: 45.8,
      image: "/placeholder.svg?height=60&width=60",
      spec: "5kg",
      quantity: 1,
    },
  ]

  const coupons = [
    { id: 1, name: "满100减20", discount: 20, minAmount: 100 },
    { id: 2, name: "新用户专享券", discount: 15, minAmount: 50 },
    { id: 3, name: "生鲜专用券", discount: 10, minAmount: 30 },
  ]

  const subtotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
  const deliveryFee = deliveryMethod === "pickup" ? 0 : 5
  const couponDiscount = selectedCoupon ? coupons.find((c) => c.id.toString() === selectedCoupon)?.discount || 0 : 0
  const total = subtotal + deliveryFee - couponDiscount

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white sticky top-0 z-50 border-b">
        <div className="flex items-center justify-between p-4">
          <Link href="/cart">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </Link>
          <span className="font-medium">确认订单</span>
          <div className="w-10"></div>
        </div>
      </header>

      <div className="pb-24">
        {/* Delivery Address */}
        <Card className="m-4">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <MapPin className="w-5 h-5 text-blue-500" />
                <span className="font-medium">收货地址</span>
              </div>
              <Link href="/profile/address">
                <Button variant="ghost" size="sm">
                  <Plus className="w-4 h-4 mr-1" />
                  新增
                </Button>
              </Link>
            </div>

            <RadioGroup
              value={selectedAddress.toString()}
              onValueChange={(value) => setSelectedAddress(Number.parseInt(value))}
            >
              {addresses.map((addr) => (
                <div key={addr.id} className="flex items-start gap-3 p-3 border rounded-lg">
                  <RadioGroupItem value={addr.id.toString()} id={`address-${addr.id}`} className="mt-1" />
                  <Label htmlFor={`address-${addr.id}`} className="flex-1 cursor-pointer">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{addr.name}</span>
                      <span className="text-gray-600">{addr.phone}</span>
                      {addr.isDefault && (
                        <Badge variant="secondary" className="text-xs">
                          默认
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{addr.address}</p>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Order Items */}
        <Card className="mx-4 mb-4">
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">商品清单</h3>
            <div className="space-y-3">
              {cartItems.map((item) => (
                <div key={item.id} className="flex items-center gap-3">
                  <StaticImage
                    src={item.image || "/placeholder.svg"}
                    alt={item.name}
                    width={60}
                    height={60}
                    className="w-15 h-15 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-sm line-clamp-1">{item.name}</h4>
                    <p className="text-xs text-gray-500">{item.spec}</p>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-red-500 font-bold">¥{item.price}</span>
                      <span className="text-sm text-gray-500">x{item.quantity}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Delivery Method */}
        <Card className="mx-4 mb-4">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Truck className="w-5 h-5 text-blue-500" />
              <span className="font-medium">配送方式</span>
            </div>

            <RadioGroup value={deliveryMethod} onValueChange={setDeliveryMethod}>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <Label htmlFor="express" className="flex items-center gap-3 cursor-pointer">
                  <RadioGroupItem value="express" id="express" />
                  <div>
                    <div className="font-medium">快递配送</div>
                    <div className="text-sm text-gray-500">预计2-3天送达</div>
                  </div>
                </Label>
                <span className="text-sm">¥5</span>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <Label htmlFor="pickup" className="flex items-center gap-3 cursor-pointer">
                  <RadioGroupItem value="pickup" id="pickup" />
                  <div>
                    <div className="font-medium">门店自提</div>
                    <div className="text-sm text-gray-500">三里屯店 - 营业时间 9:00-21:00</div>
                  </div>
                </Label>
                <span className="text-sm text-green-600">免费</span>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Coupons */}
        <Card className="mx-4 mb-4">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Gift className="w-5 h-5 text-blue-500" />
              <span className="font-medium">优惠券</span>
            </div>

            <RadioGroup value={selectedCoupon} onValueChange={setSelectedCoupon}>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <Label htmlFor="no-coupon" className="flex items-center gap-3 cursor-pointer">
                  <RadioGroupItem value="" id="no-coupon" />
                  <span>不使用优惠券</span>
                </Label>
              </div>

              {coupons.map((coupon) => (
                <div key={coupon.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <Label htmlFor={`coupon-${coupon.id}`} className="flex items-center gap-3 cursor-pointer">
                    <RadioGroupItem
                      value={coupon.id.toString()}
                      id={`coupon-${coupon.id}`}
                      disabled={subtotal < coupon.minAmount}
                    />
                    <div>
                      <div className="font-medium">{coupon.name}</div>
                      <div className="text-sm text-gray-500">满¥{coupon.minAmount}可用</div>
                    </div>
                  </Label>
                  <span className="text-red-500 font-bold">-¥{coupon.discount}</span>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Payment Method */}
        <Card className="mx-4 mb-4">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <CreditCard className="w-5 h-5 text-blue-500" />
              <span className="font-medium">支付方式</span>
            </div>

            <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <Label htmlFor="wechat" className="flex items-center gap-3 cursor-pointer">
                  <RadioGroupItem value="wechat" id="wechat" />
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-500 rounded text-white text-xs flex items-center justify-center">
                      微
                    </div>
                    <span>微信支付</span>
                  </div>
                </Label>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <Label htmlFor="alipay" className="flex items-center gap-3 cursor-pointer">
                  <RadioGroupItem value="alipay" id="alipay" />
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-500 rounded text-white text-xs flex items-center justify-center">
                      支
                    </div>
                    <span>支付宝</span>
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Order Remark */}
        <Card className="mx-4 mb-4">
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">订单备注</h3>
            <Textarea
              placeholder="请输入订单备注（选填）"
              value={remark}
              onChange={(e) => setRemark(e.target.value)}
              className="min-h-[80px]"
            />
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card className="mx-4">
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">费用明细</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>商品小计</span>
                <span>¥{subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>配送费</span>
                <span>{deliveryFee === 0 ? "免费" : `¥${deliveryFee.toFixed(2)}`}</span>
              </div>
              {couponDiscount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>优惠券</span>
                  <span>-¥{couponDiscount.toFixed(2)}</span>
                </div>
              )}
              <div className="border-t pt-2 flex justify-between font-bold text-lg">
                <span>实付金额</span>
                <span className="text-red-500">¥{total.toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Submit */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="text-right">
            <div className="text-sm text-gray-500">共{cartItems.length}件商品</div>
            <div className="text-xl font-bold text-red-500">¥{total.toFixed(2)}</div>
          </div>
        </div>
        <Button className="w-full bg-red-500 hover:bg-red-600 h-12">提交订单</Button>
      </div>
    </div>
  )
}
