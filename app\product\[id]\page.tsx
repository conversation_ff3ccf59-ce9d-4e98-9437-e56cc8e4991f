import ProductDetailPageClient from "./ProductDetailPageClient"

export async function generateStaticParams() {
  // Generate static params for product pages
  const productIds = ["1", "2", "3", "4", "5", "6"]

  return productIds.map((id) => ({
    id: id,
  }))
}

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  return <ProductDetailPageClient params={params} />
}
