"use client"

import { useState } from "react"
import Link from "next/link"
import { MapPin, Heart, Gift, Headphones, Settings, ChevronRight, Bell, Shield, HelpCircle, LogOut } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

export default function ProfilePage() {
  const [user] = useState({
    name: "张三",
    phone: "138****8888",
    avatar: "/placeholder.svg?height=80&width=80",
    level: "黄金会员",
    points: 1280,
    coupons: 5,
  })

  const orderStats = [
    { label: "待支付", count: 2, icon: "💰" },
    { label: "待收货", count: 1, icon: "📦" },
    { label: "待评价", count: 3, icon: "⭐" },
    { label: "售后", count: 0, icon: "🔄" },
  ]

  const menuItems = [
    {
      title: "我的服务",
      items: [
        { icon: <MapPin className="w-5 h-5" />, label: "收货地址", href: "/profile/address" },
        { icon: <Heart className="w-5 h-5" />, label: "我的收藏", href: "/profile/favorites", badge: "12" },
        {
          icon: <Gift className="w-5 h-5" />,
          label: "优惠券",
          href: "/profile/coupons",
          badge: user.coupons.toString(),
        },
        { icon: <Headphones className="w-5 h-5" />, label: "联系客服", href: "/profile/support" },
      ],
    },
    {
      title: "设置",
      items: [
        { icon: <Bell className="w-5 h-5" />, label: "消息通知", href: "/profile/notifications" },
        { icon: <Shield className="w-5 h-5" />, label: "隐私设置", href: "/profile/privacy" },
        { icon: <HelpCircle className="w-5 h-5" />, label: "帮助中心", href: "/profile/help" },
        { icon: <Settings className="w-5 h-5" />, label: "设置", href: "/profile/settings" },
      ],
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="p-4 pt-12">
          <div className="flex items-center gap-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src={user.avatar || "/placeholder.svg"} />
              <AvatarFallback>{user.name[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="text-xl font-bold">{user.name}</h2>
              <p className="text-blue-100">{user.phone}</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className="bg-yellow-500 text-white">
                  {user.level}
                </Badge>
                <span className="text-sm">积分: {user.points}</span>
              </div>
            </div>
            <Button variant="ghost" size="icon" className="text-white">
              <Settings className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </header>

      {/* Order Status */}
      <Card className="mx-4 -mt-6 relative z-10">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium">我的订单</h3>
            <Link href="/orders" className="text-sm text-blue-500 flex items-center gap-1">
              查看全部
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>
          <div className="grid grid-cols-4 gap-4">
            {orderStats.map((stat, index) => (
              <Link key={index} href="/orders" className="text-center">
                <div className="text-2xl mb-1">{stat.icon}</div>
                <div className="text-sm text-gray-600 mb-1">{stat.label}</div>
                {stat.count > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {stat.count}
                  </Badge>
                )}
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Menu Items */}
      <div className="p-4 space-y-4">
        {menuItems.map((section, sectionIndex) => (
          <Card key={sectionIndex}>
            <CardContent className="p-0">
              <div className="p-4 border-b">
                <h3 className="font-medium text-gray-800">{section.title}</h3>
              </div>
              <div className="divide-y">
                {section.items.map((item, itemIndex) => (
                  <Link key={itemIndex} href={item.href}>
                    <div className="flex items-center justify-between p-4 hover:bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className="text-gray-600">{item.icon}</div>
                        <span>{item.label}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {item.badge && (
                          <Badge variant="destructive" className="text-xs">
                            {item.badge}
                          </Badge>
                        )}
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Logout */}
        <Card>
          <CardContent className="p-0">
            <button className="w-full flex items-center justify-center gap-2 p-4 text-red-500 hover:bg-red-50">
              <LogOut className="w-5 h-5" />
              <span>退出登录</span>
            </button>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="flex">
          <Link href="/" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/category" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">📂</div>
            <span className="text-xs">分类</span>
          </Link>
          <Link href="/cart" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">🛒</div>
            <span className="text-xs">购物车</span>
          </Link>
          <Link href="/orders" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">📋</div>
            <span className="text-xs">订单</span>
          </Link>
          <Link href="/profile" className="flex-1 flex flex-col items-center py-2 text-blue-500">
            <div className="w-6 h-6 mb-1">👤</div>
            <span className="text-xs">我的</span>
          </Link>
        </div>
      </nav>

      <div className="h-16"></div>
    </div>
  )
}
