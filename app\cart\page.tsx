"use client"

import { useState } from "react"
import { StaticImage } from "@/components/static-image"
import Link from "next/link"
import { Arrow<PERSON><PERSON>t, Minus, Plus, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"

export default function CartPage() {
  const [cartItems, setCartItems] = useState([
    {
      id: 1,
      name: "新鲜有机苹果",
      price: 28.8,
      originalPrice: 35.8,
      image: "/placeholder.svg?height=80&width=80",
      spec: "500g",
      quantity: 2,
      selected: true,
      stock: 999,
    },
    {
      id: 2,
      name: "优质大米",
      price: 45.8,
      originalPrice: 52.8,
      image: "/placeholder.svg?height=80&width=80",
      spec: "5kg",
      quantity: 1,
      selected: true,
      stock: 100,
    },
    {
      id: 3,
      name: "新鲜蔬菜包",
      price: 25.8,
      originalPrice: 30.8,
      image: "/placeholder.svg?height=80&width=80",
      spec: "混合装",
      quantity: 1,
      selected: false,
      stock: 50,
    },
  ])

  const [selectAll, setSelectAll] = useState(false)

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity < 1) return
    setCartItems((items) => items.map((item) => (item.id === id ? { ...item, quantity: newQuantity } : item)))
  }

  const toggleItemSelection = (id: number) => {
    setCartItems((items) => items.map((item) => (item.id === id ? { ...item, selected: !item.selected } : item)))
  }

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll
    setSelectAll(newSelectAll)
    setCartItems((items) => items.map((item) => ({ ...item, selected: newSelectAll })))
  }

  const removeItem = (id: number) => {
    setCartItems((items) => items.filter((item) => item.id !== id))
  }

  const selectedItems = cartItems.filter((item) => item.selected)
  const totalPrice = selectedItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
  const totalOriginalPrice = selectedItems.reduce((sum, item) => sum + item.originalPrice * item.quantity, 0)
  const savings = totalOriginalPrice - totalPrice

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white sticky top-0 z-50 border-b">
        <div className="flex items-center justify-between p-4">
          <Link href="/">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </Link>
          <span className="font-medium">购物车({cartItems.length})</span>
          <Button variant="ghost" className="text-sm">
            编辑
          </Button>
        </div>
      </header>

      {cartItems.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-96">
          <div className="text-6xl mb-4">🛒</div>
          <p className="text-gray-500 mb-4">购物车空空如也</p>
          <Link href="/">
            <Button>去逛逛</Button>
          </Link>
        </div>
      ) : (
        <>
          {/* Cart Items */}
          <div className="p-4 space-y-3">
            {cartItems.map((item) => (
              <Card key={item.id}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Checkbox checked={item.selected} onCheckedChange={() => toggleItemSelection(item.id)} />
                    <StaticImage
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      width={80}
                      height={80}
                      className="w-20 h-20 object-cover rounded"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium mb-1 line-clamp-2">{item.name}</h3>
                      <p className="text-sm text-gray-500 mb-2">{item.spec}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-red-500 font-bold">¥{item.price}</span>
                          <span className="text-gray-400 line-through text-sm">¥{item.originalPrice}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="w-8 h-8 bg-transparent"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="w-3 h-3" />
                          </Button>
                          <span className="w-8 text-center">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="w-8 h-8 bg-transparent"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="icon" className="text-gray-400" onClick={() => removeItem(item.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Recommended Products */}
          <div className="bg-white p-4 mb-20">
            <h3 className="font-medium mb-3">为你推荐</h3>
            <div className="grid grid-cols-2 gap-3">
              {[1, 2].map((id) => (
                <Card key={id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <StaticImage
                      src="/placeholder.svg?height=120&width=120"
                      alt="推荐商品"
                      width={120}
                      height={120}
                      className="w-full h-24 object-cover"
                    />
                    <div className="p-2">
                      <h4 className="text-sm font-medium mb-1 line-clamp-1">推荐商品 {id}</h4>
                      <span className="text-red-500 font-bold text-sm">¥19.9</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Bottom Checkout */}
          <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Checkbox checked={selectAll} onCheckedChange={toggleSelectAll} />
                <span className="text-sm">全选</span>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">合计:</span>
                  <span className="text-xl font-bold text-red-500">¥{totalPrice.toFixed(2)}</span>
                </div>
                {savings > 0 && <div className="text-xs text-green-600">已省¥{savings.toFixed(2)}</div>}
              </div>
            </div>
            <div className="flex gap-3">
              <Link href="/checkout" className="flex-1">
                <Button className="w-full bg-red-500 hover:bg-red-600" disabled={selectedItems.length === 0}>
                  结算({selectedItems.length})
                </Button>
              </Link>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
