"use client"

import { useState } from "react"
import { StaticImage } from "@/components/static-image"
import Link from "next/link"
import { Search, Filter } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"

export default function CategoryPage() {
  const [selectedCategory, setSelectedCategory] = useState(1)

  const categories = [
    { id: 1, name: "生鲜蔬菜", icon: "🥬" },
    { id: 2, name: "新鲜水果", icon: "🍎" },
    { id: 3, name: "肉禽蛋类", icon: "🥩" },
    { id: 4, name: "海鲜水产", icon: "🐟" },
    { id: 5, name: "米面粮油", icon: "🌾" },
    { id: 6, name: "调料干货", icon: "🧂" },
    { id: 7, name: "休闲零食", icon: "🍪" },
    { id: 8, name: "酒水饮料", icon: "🥤" },
  ]

  const products = [
    {
      id: 1,
      name: "新鲜有机苹果",
      price: 28.8,
      originalPrice: 35.8,
      image: "/placeholder.svg?height=150&width=150",
      rating: 4.8,
      sales: 1200,
    },
    {
      id: 2,
      name: "精选牛肉",
      price: 68.8,
      originalPrice: 78.8,
      image: "/placeholder.svg?height=150&width=150",
      rating: 4.9,
      sales: 800,
    },
    {
      id: 3,
      name: "新鲜蔬菜包",
      price: 25.8,
      originalPrice: 30.8,
      image: "/placeholder.svg?height=150&width=150",
      rating: 4.7,
      sales: 600,
    },
    {
      id: 4,
      name: "优质大米",
      price: 45.8,
      originalPrice: 52.8,
      image: "/placeholder.svg?height=150&width=150",
      rating: 4.8,
      sales: 900,
    },
    {
      id: 5,
      name: "新鲜海鲜",
      price: 88.8,
      originalPrice: 98.8,
      image: "/placeholder.svg?height=150&width=150",
      rating: 4.6,
      sales: 400,
    },
    {
      id: 6,
      name: "有机蔬菜",
      price: 35.8,
      originalPrice: 42.8,
      image: "/placeholder.svg?height=150&width=150",
      rating: 4.9,
      sales: 700,
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white sticky top-0 z-50 border-b">
        <div className="flex items-center gap-3 p-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="搜索商品" className="pl-10 rounded-full" />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="w-4 h-4" />
          </Button>
        </div>
      </header>

      <div className="flex h-[calc(100vh-120px)]">
        {/* Category Sidebar */}
        <div className="w-24 bg-white border-r overflow-y-auto">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`w-full p-4 text-center border-b ${
                selectedCategory === category.id
                  ? "bg-blue-50 text-blue-600 border-r-2 border-r-blue-600"
                  : "text-gray-600"
              }`}
            >
              <div className="text-2xl mb-1">{category.icon}</div>
              <div className="text-xs leading-tight">{category.name}</div>
            </button>
          ))}
        </div>

        {/* Products Grid */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-bold text-lg">{categories.find((c) => c.id === selectedCategory)?.name}</h2>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>共{products.length}件商品</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {products.map((product) => (
                <Link key={product.id} href={`/product/${product.id}`}>
                  <Card className="overflow-hidden">
                    <CardContent className="p-0">
                      <StaticImage
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        width={150}
                        height={150}
                        className="w-full h-32 object-cover"
                      />
                      <div className="p-3">
                        <h4 className="font-medium mb-2 line-clamp-2 text-sm">{product.name}</h4>
                        <div className="flex items-center gap-1 mb-2">
                          <span className="text-xs text-gray-600">⭐ {product.rating}</span>
                          <span className="text-xs text-gray-400">已售{product.sales}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            <span className="text-red-500 font-bold text-sm">¥{product.price}</span>
                            <span className="text-gray-400 line-through text-xs">¥{product.originalPrice}</span>
                          </div>
                          <Button size="sm" variant="outline" className="h-6 px-2 text-xs bg-transparent">
                            加购
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="flex">
          <Link href="/" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/category" className="flex-1 flex flex-col items-center py-2 text-blue-500">
            <div className="w-6 h-6 mb-1">📂</div>
            <span className="text-xs">分类</span>
          </Link>
          <Link href="/cart" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">🛒</div>
            <span className="text-xs">购物车</span>
          </Link>
          <Link href="/orders" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">📋</div>
            <span className="text-xs">订单</span>
          </Link>
          <Link href="/profile" className="flex-1 flex flex-col items-center py-2 text-gray-600">
            <div className="w-6 h-6 mb-1">👤</div>
            <span className="text-xs">我的</span>
          </Link>
        </div>
      </nav>
    </div>
  )
}
