"use client"

import { useState } from "react"
import { StaticImage } from "@/components/static-image"
import Link from "next/link"
import { ArrowLeft, Share2, Heart, Star, ShoppingCart, MessageCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export default function ProductDetailPageClient({ params }: { params: { id: string } }) {
  const [currentImage, setCurrentImage] = useState(0)
  const [selectedSpec, setSelectedSpec] = useState("500g")
  const [quantity, setQuantity] = useState(1)
  const [isFavorited, setIsFavorited] = useState(false)

  // Use the id from params to get product data
  const productId = params.id

  const product = {
    id: 1,
    name: "新鲜有机苹果",
    price: 28.8,
    originalPrice: 35.8,
    rating: 4.8,
    sales: 1200,
    stock: 999,
    images: [
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    specs: ["500g", "1kg", "2kg"],
    description: "精选优质有机苹果，口感清脆甘甜，营养丰富，无农药残留，健康安全。产地直供，新鲜配送到家。",
  }

  const reviews = [
    {
      id: 1,
      user: "张**",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
      content: "苹果很新鲜，口感很好，包装也很仔细，会回购的！",
      images: ["/placeholder.svg?height=80&width=80", "/placeholder.svg?height=80&width=80"],
      date: "2024-01-15",
    },
    {
      id: 2,
      user: "李**",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
      content: "质量不错，价格合理，配送速度很快。",
      images: [],
      date: "2024-01-14",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white sticky top-0 z-50 border-b">
        <div className="flex items-center justify-between p-4">
          <Link href="/">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </Link>
          <span className="font-medium">商品详情</span>
          <div className="flex gap-2">
            <Button variant="ghost" size="icon">
              <Share2 className="w-5 h-5" />
            </Button>
            <Button variant="ghost" size="icon" onClick={() => setIsFavorited(!isFavorited)}>
              <Heart className={`w-5 h-5 ${isFavorited ? "fill-red-500 text-red-500" : ""}`} />
            </Button>
          </div>
        </div>
      </header>

      {/* Product Images */}
      <div className="bg-white">
        <div className="relative">
          <StaticImage
            src={product.images[currentImage] || "/placeholder.svg"}
            alt={product.name}
            width={400}
            height={400}
            className="w-full h-80 object-cover"
          />
          <div className="absolute bottom-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
            {currentImage + 1}/{product.images.length}
          </div>
        </div>
        <div className="flex gap-2 p-4 overflow-x-auto">
          {product.images.map((image, index) => (
            <button
              key={index}
              onClick={() => setCurrentImage(index)}
              className={`flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden ${
                currentImage === index ? "border-blue-500" : "border-gray-200"
              }`}
            >
              <StaticImage
                src={image || "/placeholder.svg"}
                alt=""
                width={64}
                height={64}
                className="w-full h-full object-cover"
              />
            </button>
          ))}
        </div>
      </div>

      {/* Product Info */}
      <div className="bg-white p-4 mb-2">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <h1 className="text-xl font-bold mb-2">{product.name}</h1>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold text-red-500">¥{product.price}</span>
              <span className="text-gray-400 line-through">¥{product.originalPrice}</span>
              <Badge variant="destructive" className="text-xs">
                限时特价
              </Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
            <span>{product.rating}</span>
          </div>
          <span>已售{product.sales}件</span>
          <span>库存{product.stock}件</span>
        </div>

        {/* Specifications */}
        <div className="mb-4">
          <h3 className="font-medium mb-2">规格选择</h3>
          <div className="flex gap-2">
            {product.specs.map((spec) => (
              <button
                key={spec}
                onClick={() => setSelectedSpec(spec)}
                className={`px-4 py-2 rounded border text-sm ${
                  selectedSpec === spec ? "border-blue-500 bg-blue-50 text-blue-600" : "border-gray-200"
                }`}
              >
                {spec}
              </button>
            ))}
          </div>
        </div>

        {/* Quantity */}
        <div className="flex items-center justify-between">
          <span className="font-medium">数量</span>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="icon"
              className="w-8 h-8 bg-transparent"
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
            >
              -
            </Button>
            <span className="w-12 text-center">{quantity}</span>
            <Button
              variant="outline"
              size="icon"
              className="w-8 h-8 bg-transparent"
              onClick={() => setQuantity(quantity + 1)}
            >
              +
            </Button>
          </div>
        </div>
      </div>

      {/* Product Details & Reviews */}
      <div className="bg-white mb-20">
        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details">商品详情</TabsTrigger>
            <TabsTrigger value="reviews">用户评价</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="p-4">
            <div className="space-y-4">
              <p className="text-gray-600 leading-relaxed">{product.description}</p>
              <StaticImage
                src="/placeholder.svg?height=300&width=400"
                alt="Product details"
                width={400}
                height={300}
                className="w-full rounded"
              />
            </div>
          </TabsContent>

          <TabsContent value="reviews" className="p-4">
            <div className="space-y-4">
              {reviews.map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={review.avatar || "/placeholder.svg"} />
                        <AvatarFallback>{review.user[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{review.user}</span>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-3 h-3 ${i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-gray-600 mb-2">{review.content}</p>
                        {review.images.length > 0 && (
                          <div className="flex gap-2">
                            {review.images.map((img, index) => (
                              <StaticImage
                                key={index}
                                src={img || "/placeholder.svg"}
                                alt=""
                                width={80}
                                height={80}
                                className="w-16 h-16 object-cover rounded"
                              />
                            ))}
                          </div>
                        )}
                        <span className="text-xs text-gray-400 mt-2 block">{review.date}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Bottom Actions */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="flex gap-3">
          <Button variant="outline" size="icon">
            <MessageCircle className="w-5 h-5" />
          </Button>
          <Button variant="outline" className="flex-1 bg-transparent">
            <ShoppingCart className="w-4 h-4 mr-2" />
            加入购物车
          </Button>
          <Button className="flex-1 bg-red-500 hover:bg-red-600">立即购买</Button>
        </div>
      </div>
    </div>
  )
}
